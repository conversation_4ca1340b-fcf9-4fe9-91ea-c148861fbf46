<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Test - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Cart Functionality Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Test Controls -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
                
                <div class="space-y-4">
                    <button onclick="testAddToCart()" class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                        Add Test Product to Cart
                    </button>
                    
                    <button onclick="testGetCart()" class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                        Get Cart Contents
                    </button>
                    
                    <button onclick="testClearCart()" class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600">
                        Clear Cart
                    </button>
                    
                    <div class="border-t pt-4">
                        <h3 class="font-semibold mb-2">Session Info</h3>
                        <p class="text-sm text-gray-600">Session ID: <span id="session-id"></span></p>
                        <p class="text-sm text-gray-600">User Token: <span id="user-token"></span></p>
                    </div>
                </div>
            </div>
            
            <!-- Results -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Results</h2>
                <div id="results" class="space-y-2 text-sm">
                    <p class="text-gray-500">Click a test button to see results...</p>
                </div>
            </div>
        </div>
        
        <!-- Cart Display -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Current Cart</h2>
            <div id="cart-display" class="space-y-2">
                <p class="text-gray-500">Cart is empty</p>
            </div>
        </div>
    </div>

    <script src="api-config.js"></script>
    <script>
        // Display session info
        document.getElementById('session-id').textContent = window.apiClient.getSessionId();
        document.getElementById('user-token').textContent = window.apiClient.getToken() || 'None (Guest)';
        
        function logResult(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600';
            
            const div = document.createElement('div');
            div.className = `p-2 border-l-4 ${type === 'error' ? 'border-red-500 bg-red-50' : type === 'success' ? 'border-green-500 bg-green-50' : 'border-blue-500 bg-blue-50'}`;
            div.innerHTML = `<span class="text-xs text-gray-500">[${timestamp}]</span> <span class="${color}">${message}</span>`;
            
            results.insertBefore(div, results.firstChild);
            
            // Keep only last 10 results
            while (results.children.length > 10) {
                results.removeChild(results.lastChild);
            }
        }
        
        async function testAddToCart() {
            try {
                logResult('Adding test product to cart...');
                const response = await window.apiClient.addToCart(1, 2, 'Blue', 'M');
                logResult(`Success: ${response.message}`, 'success');
                await updateCartDisplay();
            } catch (error) {
                logResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function testGetCart() {
            try {
                logResult('Fetching cart contents...');
                const response = await window.apiClient.getCart();
                logResult(`Success: Found ${response.data?.items?.length || 0} items in cart`, 'success');
                await updateCartDisplay();
            } catch (error) {
                logResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function testClearCart() {
            try {
                logResult('Clearing cart...');
                const response = await window.apiClient.clearCart();
                logResult(`Success: ${response.message}`, 'success');
                await updateCartDisplay();
            } catch (error) {
                logResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function updateCartDisplay() {
            try {
                const response = await window.apiClient.getCart();
                const cartDisplay = document.getElementById('cart-display');
                
                if (response.success && response.data?.items?.length > 0) {
                    cartDisplay.innerHTML = response.data.items.map(item => `
                        <div class="border p-3 rounded">
                            <div class="font-semibold">${item.product_name || 'Product #' + item.product_id}</div>
                            <div class="text-sm text-gray-600">
                                Quantity: ${item.quantity} | Price: $${item.price}
                                ${item.selected_color ? ` | Color: ${item.selected_color}` : ''}
                                ${item.selected_size ? ` | Size: ${item.selected_size}` : ''}
                            </div>
                            <button onclick="removeItem(${item.id})" class="mt-2 text-red-500 text-sm hover:text-red-700">
                                Remove
                            </button>
                        </div>
                    `).join('');
                } else {
                    cartDisplay.innerHTML = '<p class="text-gray-500">Cart is empty</p>';
                }
            } catch (error) {
                document.getElementById('cart-display').innerHTML = `<p class="text-red-500">Error loading cart: ${error.message}</p>`;
            }
        }
        
        async function removeItem(itemId) {
            try {
                logResult(`Removing item ${itemId}...`);
                const response = await window.apiClient.removeFromCart(itemId);
                logResult(`Success: ${response.message}`, 'success');
                await updateCartDisplay();
            } catch (error) {
                logResult(`Error: ${error.message}`, 'error');
            }
        }
        
        // Load cart on page load
        updateCartDisplay();
    </script>
</body>
</html>
